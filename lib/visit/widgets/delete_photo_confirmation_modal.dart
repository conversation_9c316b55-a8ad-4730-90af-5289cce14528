import 'dart:io';

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/widgets/loading_widget.dart';
import 'package:serwis_app/visit/cubit/visit_photos_cubit.dart';

class DeletePhotoConfirmationModal extends StatelessWidget {
  final File photoFile;
  final VisitPhotosCubit cubit;
  final bool isDeleting;

  const DeletePhotoConfirmationModal({
    super.key,
    required this.photoFile,
    required this.cubit,
    required this.isDeleting,
  });

  static Future<bool?> show(
    BuildContext context, {
    required File photoFile,
    required VisitPhotosCubit cubit,
  }) {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocSelector<VisitPhotosCubit, VisitPhotosState, bool>(
        bloc: cubit,
        selector: (state) => state.status == VisitPhotosStatus.deletingPhoto,
        builder: (context, isDeleting) {
          return PopScope(
            canPop: !isDeleting,
            child: DeletePhotoConfirmationModal(
              photoFile: photoFile,
              cubit: cubit,
              isDeleting: isDeleting,
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return BlocListener<VisitPhotosCubit, VisitPhotosState>(
      bloc: cubit,
      listener: (context, state) {
        if (state.status == VisitPhotosStatus.ready && state.error == null) {
          Navigator.of(context).pop();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHigh,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Tytuł
            Text(
              'Usuń zdjęcie',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
            ),
            const SizedBox(height: 8),

            // Opis
            Text(
              'Czy na pewno chcesz usunąć to zdjęcie?',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Podgląd zdjęcia
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: SizedBox(
                width: 160,
                height: 160,
                child: Image.file(
                  photoFile,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: colorScheme.surfaceContainerHighest,
                      child: Icon(
                        Icons.broken_image,
                        size: 40,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 32),

            // Przyciski
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: !isDeleting ? () => Navigator.of(context).pop(false) : null,
                    child: const Text('Anuluj'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: LoadingWidget(
                    isLoading: isDeleting,
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          cubit.deletePhoto(photoFile);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.errorContainer,
                          foregroundColor: colorScheme.onErrorContainer,
                        ),
                        child: const Text('Usuń'),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Dodatkowy padding dla bezpiecznego obszaru
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }
}
